#!/bin/bash

# 修复所有Spring Boot模块的Maven插件配置问题
# 1. 将Spring Boot插件从profile移到主build配置
# 2. 添加正确的mainClass配置

echo "开始修复所有Spring Boot模块..."

# 定义模块和对应的主类映射
declare -A modules=(
    ["bjgy-cloud-gateway"]="com.bjgy.GatewayApplication"
    ["bjgy-cloud-system"]="com.bjgy.SystemApplication"
    ["bjgy-cloud-data-integrate"]="com.bjgy.DataIntegrateApplication"
    ["bjgy-cloud-data-development/bjgy-cloud-develop-server"]="com.bjgy.DataDevelopmentApplication"
    ["bjgy-cloud-data-service"]="com.bjgy.DataServiceApplication"
    ["bjgy-cloud-data-governance"]="com.bjgy.DataGovernanceApplication"
    ["bjgy-cloud-data-assets"]="com.bjgy.DataAssetsApplication"
    ["bjgy-cloud-data-bi"]="com.bjgy.DataBiApplication"
    ["bjgy-cloud-module/bjgy-cloud-monitor"]="com.bjgy.MonitorApplication"
    ["bjgy-cloud-module/bjgy-cloud-message"]="com.bjgy.MessageApplication"
)

# 处理每个模块
for module in "${!modules[@]}"; do
    pom_file="./${module}/pom.xml"
    main_class="${modules[$module]}"
    
    if [ ! -f "$pom_file" ]; then
        echo "警告: 未找到文件 $pom_file"
        continue
    fi
    
    echo "处理模块: $module (主类: $main_class)"
    
    # 检查是否已经在主build中有Spring Boot插件
    if grep -A 20 "^[[:space:]]*<build>" "$pom_file" | grep -q "spring-boot-maven-plugin"; then
        echo "  - 主build中已存在Spring Boot插件，跳过"
        continue
    fi
    
    # 检查是否在dependencies后面有</dependencies>标签，用于插入build配置
    if ! grep -q "</dependencies>" "$pom_file"; then
        echo "  - 未找到</dependencies>标签，跳过"
        continue
    fi
    
    # 创建临时文件
    temp_file=$(mktemp)
    
    # 在</dependencies>后添加build配置
    awk -v main_class="$main_class" '
    /<\/dependencies>/ {
        print $0
        print ""
        print "    <build>"
        print "        <plugins>"
        print "            <plugin>"
        print "                <groupId>org.springframework.boot</groupId>"
        print "                <artifactId>spring-boot-maven-plugin</artifactId>"
        print "                <configuration>"
        print "                    <mainClass>" main_class "</mainClass>"
        print "                </configuration>"
        print "                <executions>"
        print "                    <execution>"
        print "                        <goals>"
        print "                            <goal>repackage</goal>"
        print "                        </goals>"
        print "                    </execution>"
        print "                </executions>"
        print "            </plugin>"
        print "        </plugins>"
        print "    </build>"
        next
    }
    { print }
    ' "$pom_file" > "$temp_file"
    
    # 替换原文件
    mv "$temp_file" "$pom_file"
    echo "  - 已添加Spring Boot插件到主build配置"
done

echo "所有Spring Boot模块修复完成！"
echo ""
echo "现在可以运行以下命令测试打包："
echo "mvn clean package -DskipTests"
