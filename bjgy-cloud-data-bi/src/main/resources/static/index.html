<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
  <head>
    <script>self["MonacoEnvironment"] = (function (paths) {
          return {
            globalAPI: false,
            getWorkerUrl : function (moduleId, label) {
              var result =  paths[label];
              if (/^((http:)|(https:)|(file:)|(\/\/))/.test(result)) {
                var currentUrl = String(window.location);
                var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);
                if (result.substring(0, currentOrigin.length) !== currentOrigin) {
                  var js = '/*' + label + '*/importScripts("' + result + '");';
                  var blob = new Blob([js], { type: 'application/javascript' });
                  return URL.createObjectURL(blob);
                }
              }
              return result;
            }
          };
        })({
  "editorWorkerService": "/data-bi/monacoeditorwork/editor.worker.bundle.js",
  "typescript": "/data-bi/monacoeditorwork/ts.worker.bundle.js",
  "json": "/data-bi/monacoeditorwork/json.worker.bundle.js",
  "html": "/data-bi/monacoeditorwork/html.worker.bundle.js",
  "javascript": "/data-bi/monacoeditorwork/ts.worker.bundle.js",
  "handlebars": "/data-bi/monacoeditorwork/html.worker.bundle.js",
  "razor": "/data-bi/monacoeditorwork/html.worker.bundle.js"
});</script>

    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <link rel="icon" href="./favicon.ico" />
    <title>数据可视化</title>
    
    <script type="module" crossorigin src="/data-bi/static/js/index-496f6074.js"></script>
    <link rel="stylesheet" href="/data-bi/static/css/index-3c85b338.css">
  </head>
  <body>
    <div id="appProvider" style="display: none"></div>
    <div id="app">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
      </div>
    </div>
    
  </body>
</html>
