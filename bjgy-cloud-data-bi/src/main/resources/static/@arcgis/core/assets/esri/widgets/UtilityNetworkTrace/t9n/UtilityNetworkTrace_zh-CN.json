{"widgetLabel": "公共设施追踪", "widgetDescription": "用于在公共设施网络中执行追踪的微件。", "globalStrings": {"selectTerminalPlaceholder": "选择终端", "locateFeature": "定位", "zoomToFeature": "缩放至", "clearResults": "放弃", "nonSpatialObject": "非空间对象"}, "inputsStrings": {"headerTabInputs": "输入", "headerStartingPoint": "起点", "startingPointHint": "添加追踪起点。", "headerBarrier": "障碍", "barrierButton": "障碍", "barrierPointHint": "添加追踪障碍点。", "barrierFilter": "过滤器障碍", "addPointOption": "添加点", "addPointHint": "单击地图添加点。", "removeAllPoints": "全部移除", "selectTraces": "选择轨迹"}, "tracingStrings": {"traceOperation": "追踪类型", "runTrace": "运行"}, "resultsStrings": {"headerTabResults": "结果", "resultsListOptions": "选项", "ellipsesOptions": "选项", "displayAttribute": "显示属性", "sortBy": "排序方式", "startOverButton": "全部清除", "startOverValidation": "是否确定? 所有追踪“输入”和“结果”均将丢失。", "highlightTrace": "显示图形", "noSelectionResults": "无法选择记录。 启用以显示图形。", "viewFeatures": "记录", "selectFeatures": "选择记录", "graphicColor": "颜色", "functionHeader": "函数", "noValue": "没有值", "resultAreaHeader": "显示结果区域", "resultAreaBuffer": "缓冲区", "noFeaturesInMap": "未列出任何记录，因为地图中缺少这些记录。"}, "alertsStrings": {"startingPointAlertHeader": "起点缺失", "startingPointAlert": "添加一个起点来运行此追踪。", "selectTraceAlertHeader": "未选择追踪类型", "selectTraceAlert": "选择要运行的追踪类型。", "NoRunAlertHeader": "无可用结果", "noResultsInfo": "转至“输入”选项卡以选择追踪类型，添加起点，然后单击“运行”来查看结果。", "traceExecuting": "追踪进行中...", "genericResultHeader": "追踪结果", "genericErrorHeader": "发生错误", "noAssetsFoundHeader": "无法添加点", "noAssetFound": "该点必须与地图上的要素相交。", "clearResultsAlert": "是否确定? 将丢弃此追踪的所有结果。", "noTraceSupported": "未找到公共设施网络或者不支持追踪。 请提供具有共享追踪配置的有效公共设施网络。", "noUserExtension": "登录的帐户缺少 ArcGIS Utility Network 用户类型扩展模块的许可。", "noTerminalDefinedHeader": "缺少终端", "noTerminalDefined": "必须至少选择一个终端。"}, "attributeStrings": {"traceId": "追踪 ID", "traceName": "追踪名称", "traceDescription": "追踪描述", "startingPoints": "起点", "barriers": "障碍", "username": "用户", "version": "版本", "date": "日期", "elementCount": "记录计数", "functionResult": "追踪功能", "areaStatistic": "区域统计", "globalid": "全局 ID", "terminalid": "终端 ID", "x": "x", "y": "y"}}