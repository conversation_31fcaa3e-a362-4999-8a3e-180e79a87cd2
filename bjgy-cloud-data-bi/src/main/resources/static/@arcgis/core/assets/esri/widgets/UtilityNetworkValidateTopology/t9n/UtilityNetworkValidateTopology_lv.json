{"label": "<PERSON><PERSON><PERSON><PERSON> tīkla topoloģiju", "input": {"currentExtent": "Pašreiz<PERSON><PERSON><PERSON>", "entireExtent": "<PERSON><PERSON>", "extentToValidate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas j<PERSON><PERSON>", "validateTopology": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"executing": "<PERSON><PERSON><PERSON><PERSON>...", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "Validācija bija sekmīga"}, "info": {"cannotAcquireVersionLock": "<PERSON><PERSON><PERSON><PERSON>ās versiju ir bloķējusi cita lietotne.", "noAdvancedEditingUserTypeExtension": "Pieteiktajam kontam trūkst Advanced Editing lietotāja veida paplašinājuma licences.", "noDirtyAreasInExtent": "Validācijas laikā nav atrasta neviena melnraksta zona.", "noDirtyAreasLayer": "Nevarēja atrast melnraksta zonu slāni. <PERSON><PERSON><PERSON> kartei melnraksta zonu slāni.", "noUtilityNetwork": "Utility Network nav norādīts. Logrīks ir jāinicializē Utility Network.", "noUtilityNetworkServiceUserTypeExtension": "Pieteiktajam kontam trūkst ArcGIS Utility Network lietotāja veida paplašinājuma licences.", "noView": "MapView netika nodrošināts. Logrīks ir jāinicializē ar MapView."}}