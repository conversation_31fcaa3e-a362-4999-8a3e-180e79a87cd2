{"label": "Validate network topology", "input": {"currentExtent": "Current extent", "entireExtent": "Entire extent", "extentToValidate": "Extent to validate", "validateTopology": "Validate"}, "status": {"executing": "Executing", "loading": "Loading", "success": "Validate successful"}, "info": {"cannotAcquireVersionLock": "Branch version is locked by another application.", "noAdvancedEditingUserTypeExtension": "The logged in account is missing a license for the Advanced Editing user type extension.", "noDirtyAreasInExtent": "No dirty area found during validate.", "noDirtyAreasLayer": "Could not find the dirty areas layer. Add the dirty areas layer to the map.", "noUtilityNetwork": "A utility network was not provided. The widget must be initialized with a utility network.", "noUtilityNetworkServiceUserTypeExtension": "The logged in account is missing a license for the ArcGIS Utility Network user type extension.", "noView": "A MapView was not provided. The widget must be initialized with a MapView."}}