{"widgetLabel": "Utility Network Trace", "widgetDescription": "A widget to perform traces in a Utility Network.", "globalStrings": {"selectTerminalPlaceholder": "Select terminals", "locateFeature": "Locate", "zoomToFeature": "Zoom to", "clearResults": "Discard", "nonSpatialObject": "Nonspatial object"}, "inputsStrings": {"headerTabInputs": "Inputs", "headerStartingPoint": "Starting points", "startingPointHint": "Add starting points for the trace.", "headerBarrier": "Barriers", "barrierButton": "Barrier", "barrierPointHint": "Add barrier points for the trace.", "barrierFilter": "Filter barrier", "addPointOption": "Add a point", "addPointHint": "Click the map to add a point", "removeAllPoints": "Remove all", "selectTraces": "Select traces"}, "tracingStrings": {"traceOperation": "Trace types", "runTrace": "Run"}, "resultsStrings": {"headerTabResults": "Results", "resultsListOptions": "Options", "ellipsesOptions": "Options", "displayAttribute": "Display attribute", "sortBy": "Sort by", "startOverButton": "Clear all", "startOverValidation": "Are you sure? All the trace Inputs and Results will be lost.", "highlightTrace": "Show graphics", "noSelectionResults": "Records cannot be selected. Enable to show graphics instead.", "viewFeatures": "Records", "selectFeatures": "Select records", "graphicColor": "Color", "functionHeader": "Functions", "noValue": "No value", "resultAreaHeader": "Show result area", "resultAreaBuffer": "<PERSON><PERSON><PERSON>", "noFeaturesInMap": "No records are listed because they are missing from the map."}, "alertsStrings": {"startingPointAlertHeader": "Starting point missing", "startingPointAlert": "Add a starting point to run this trace.", "selectTraceAlertHeader": "Trace type not selected", "selectTraceAlert": "Select a trace type to run.", "NoRunAlertHeader": "No results available", "noResultsInfo": "Go to the Inputs tab to select a trace type, add a starting point, and click Run to view the results.", "traceExecuting": "Trace in progress...", "genericResultHeader": "Trace result", "genericErrorHeader": "An error occurred", "noAssetsFoundHeader": "Cannot add point", "noAssetFound": "The point must intersect with a feature on the map.", "clearResultsAlert": "Are you sure? All the results for this trace will be discarded.", "noTraceSupported": "A Utility Network cannot be found or tracing is not supported. Provide a valid Utility Network with shared Trace Configurations.", "noUserExtension": "The account logged in is missing a license for the ArcGIS Utility Network user type extension.", "noTerminalDefinedHeader": "Terminal missing", "noTerminalDefined": "At least one terminal must be selected."}, "attributeStrings": {"traceId": "Trace Id", "traceName": "Trace name", "traceDescription": "Trace description", "startingPoints": "Starting points", "barriers": "Barriers", "username": "User", "version": "Version", "date": "Date", "elementCount": "Count of records", "functionResult": "Trace functions", "areaStatistic": "Area statistic", "globalid": "Global Id", "terminalid": "Terminal Id", "x": "x", "y": "y"}}