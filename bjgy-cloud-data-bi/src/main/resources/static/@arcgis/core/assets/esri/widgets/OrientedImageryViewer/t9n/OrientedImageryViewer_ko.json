{"title": "방향 영상 뷰어", "currentFootprint": "현재 풋프린트", "additionalFootprints": "추가 풋프린트", "additionalCameraLocations": "추가 카메라 위치", "imageAttributes": "이미지 속성", "imageEnhancement": "이미지 개선", "imageGallery": "이미지 갤러리", "float": "고정 해제", "dock": "고정", "footprint": "풋프린트", "noImageError": "선택한 위치에 사용할 수 있는 이미지가 없습니다.", "onLoadMessage": "영상을 보려면 맵에서 위치를 선택합니다.", "imageLoadError": "이미지를 불러오는 동안 오류가 발생했습니다.", "mapImageConversionTool": "맵-이미지 위치 도구", "brightness": "밝기", "contrast": "대비", "sharpness": "선명도", "navigationTool": "탐색 도구", "noLocalPort": "로컬 포트 값이 제공되지 않습니다. 로컬 이미지를 불러오려면 로컬 포트가 필요합니다.", "noAttachment": "선택한 위치에 사용할 수 있는 이미지 또는 첨부 파일이 없습니다.", "currentFootprintTooltip": "표시된 이미지 풋프린트의 표시 여부를 전환합니다.", "additionalFootprintsTooltip": "관심 위치가 포함된 모든 추가 풋프린트의 표시 여부를 전환합니다.", "additionalCameraLocationsTooltip": "관심 위치가 포함된 이미지의 모든 추가 카메라 위치에 대한 표시 여부를 전환합니다.", "navigationTooltip": "관심 위치와 관련하여 반환된 이미지를 탐색합니다.", "imageEnhancementTooltip": "이미지 밝기, 대비, 선명도를 조정합니다.", "imageGalleryTooltip": "관심 위치를 나타내는 모든 이미지의 썸네일을 탐색합니다.", "mapImageConversionTooltip": "이미지를 클릭하여 맵에서 해당 위치가 어디인지 확인할 수 있으며 그 반대의 경우 역시 확인할 수 있습니다.", "unsupportedPanoramicImageryError": "뷰어는 현재 {datasetFormat} 360도 영상 시각화를 지원하지 않습니다.", "showPopups": "팝업 표시"}