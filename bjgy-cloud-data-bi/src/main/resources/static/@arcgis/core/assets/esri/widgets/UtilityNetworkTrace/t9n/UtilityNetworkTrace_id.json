{"widgetLabel": "Pelacakan Utility Network", "widgetDescription": "Widget untuk melakukan pelacakan di Utility Network.", "globalStrings": {"selectTerminalPlaceholder": "Pilih terminal", "locateFeature": "Tentukan lokasi", "zoomToFeature": "<PERSON><PERSON> hingga", "clearResults": "<PERSON><PERSON>", "nonSpatialObject": "<PERSON><PERSON>jek nonspasial"}, "inputsStrings": {"headerTabInputs": "Input", "headerStartingPoint": "<PERSON><PERSON><PERSON> awal", "startingPointHint": "Tambahkan titik awal untuk pelacakan.", "headerBarrier": "Penghalang", "barrierButton": "Pembatas", "barrierPointHint": "Tambahkan titik pembatas untuk pelacakan.", "barrierFilter": "<PERSON>lter pembatas", "addPointOption": "Tambahkan titik", "addPointHint": "Klik peta untuk menambahkan titik", "removeAllPoints": "<PERSON><PERSON> semua", "selectTraces": "<PERSON><PERSON><PERSON>"}, "tracingStrings": {"traceOperation": "<PERSON><PERSON>", "runTrace": "Jalankan"}, "resultsStrings": {"headerTabResults": "<PERSON><PERSON>", "resultsListOptions": "Opsi", "ellipsesOptions": "Opsi", "displayAttribute": "Tampilkan atribut", "sortBy": "Uru<PERSON><PERSON>", "startOverButton": "<PERSON><PERSON> semua", "startOverValidation": "<PERSON><PERSON><PERSON><PERSON> Anda yakin? <PERSON><PERSON><PERSON> dan <PERSON><PERSON> pelacakan akan hilang.", "highlightTrace": "<PERSON><PERSON><PERSON>an grafik", "noSelectionResults": "Catatan tidak dapat dipilih. Aktifkan untuk menampilkan grafik.", "viewFeatures": "Catatan", "selectFeatures": "<PERSON><PERSON><PERSON>", "graphicColor": "<PERSON><PERSON>", "functionHeader": "<PERSON><PERSON><PERSON>", "noValue": "Tidak ada nilai", "resultAreaHeader": "Tampilkan area hasil", "resultAreaBuffer": "<PERSON><PERSON><PERSON>", "noFeaturesInMap": "Tidak ada catatan yang terdaftar karena hilang dari peta."}, "alertsStrings": {"startingPointAlertHeader": "Titik awal tidak ada", "startingPointAlert": "Tambahkan titik awal untuk menjalankan pelacakan ini.", "selectTraceAlertHeader": "<PERSON><PERSON> pela<PERSON> tidak dipilih", "selectTraceAlert": "<PERSON><PERSON><PERSON> jenis pela<PERSON>kan yang akan di<PERSON>.", "NoRunAlertHeader": "Tidak tersedia hasil", "noResultsInfo": "Buka tab Input untuk memilih jenis p<PERSON>, tambah<PERSON> titik awal, dan klik Jalankan untuk melihat hasilnya.", "traceExecuting": "Pelacakan sedang berlangsung...", "genericResultHeader": "<PERSON><PERSON>", "genericErrorHeader": "<PERSON><PERSON><PERSON><PERSON>", "noAssetsFoundHeader": "Tidak dapat menambahkan titik", "noAssetFound": "Titik ini harus berpotongan dengan fitur di peta.", "clearResultsAlert": "<PERSON><PERSON><PERSON><PERSON> Anda yakin? <PERSON><PERSON>a hasil untuk pelacakan ini akan dibuang.", "noTraceSupported": "Utility Network tidak dapat ditemukan atau pelacakan tidak didukung. Sediakan Utility Network valid dengan Konfigurasi Pelacakan bersama.", "noUserExtension": "Akun yang digunakan tidak memiliki lisensi untuk ekstensi jenis pengguna ArcGIS Utility Network.", "noTerminalDefinedHeader": "Terminal hilang", "noTerminalDefined": "Setidaknya satu terminal harus dipilih."}, "attributeStrings": {"traceId": "Id pela<PERSON>", "traceName": "<PERSON><PERSON> p<PERSON>", "traceDescription": "Deskripsi pela<PERSON>", "startingPoints": "<PERSON><PERSON><PERSON> awal", "barriers": "Penghalang", "username": "Pengguna", "version": "<PERSON><PERSON><PERSON>", "date": "Tanggal", "elementCount": "<PERSON><PERSON><PERSON> catatan", "functionResult": "Fungsi pela<PERSON>", "areaStatistic": "Area statistik", "globalid": "Id global", "terminalid": "Id terminal", "x": "x", "y": "y"}}