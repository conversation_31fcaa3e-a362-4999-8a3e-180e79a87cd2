{"label": "<PERSON><PERSON><PERSON><PERSON> tin<PERSON>lo topologij<PERSON>", "input": {"currentExtent": "<PERSON><PERSON><PERSON> a<PERSON>", "entireExtent": "Visa aprėptis", "extentToValidate": "<PERSON><PERSON><PERSON><PERSON>, kurią reikia patikrinti", "validateTopology": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"executing": "Vykdoma", "loading": "<PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"cannotAcquireVersionLock": "Šakos versiją užrakino kita programa.", "noAdvancedEditingUserTypeExtension": "<PERSON><PERSON><PERSON>, prie kurios <PERSON>, tr<PERSON>ks<PERSON> išplėstinio redagavimo teises turinčio naudotojo tipo plėtinio licencijos.", "noDirtyAreasInExtent": "Patikrinimo metu nerasta tikrintinų plotų.", "noDirtyAreasLayer": "Nepavyko rasti nepatikrintų sričių sluoksnio. Pridėkite tikrintinų plotų sluoksnį prie žemėlapio.", "noUtilityNetwork": "Nenurodytas komunalinių paslaugų tinklas. Valdiklis turi būti inicijuotas nurodant komunalinių paslaugų tinklą.", "noUtilityNetworkServiceUserTypeExtension": "<PERSON><PERSON><PERSON>, prie kurios <PERSON>, trūksta ArcGIS Utility Network naudotojo tipo plėtinio licencijos.", "noView": "MapView nesuteiktas. Valdiklis turi būti inicijuotas naudojant MapView."}}