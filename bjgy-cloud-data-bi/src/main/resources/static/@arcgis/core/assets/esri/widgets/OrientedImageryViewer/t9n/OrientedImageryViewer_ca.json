{"title": "Visualitzador d'imatges amb orientació", "currentFootprint": "Empremta actual", "additionalFootprints": "Empremtes addicionals", "additionalCameraLocations": "Ubicacions de la càmera addicionals", "imageAttributes": "Atributs d'imatge", "imageEnhancement": "<PERSON>ora de la imatge", "imageGallery": "Galeria d'imatges", "float": "Punt flotant", "dock": "Embarcador", "footprint": "Emprem<PERSON>", "noImageError": "No hi ha cap imatge disponible per a la ubicació seleccionada.", "onLoadMessage": "Seleccioneu una ubicació al mapa per veure les imatges.", "imageLoadError": "S'ha produït un error en carregar la imatge.", "mapImageConversionTool": "Eina d'ubicació de mapa-imatge", "brightness": "<PERSON><PERSON><PERSON><PERSON>", "contrast": "Contrast", "sharpness": "Nitide<PERSON>", "navigationTool": "<PERSON><PERSON>", "noLocalPort": "No s'ha proporcionat cap valor per al port local. Per carregar imatges locals, cal un port local.", "noAttachment": "No hi ha cap imatge ni fitxer adjunt disponible per a la ubicació seleccionada.", "currentFootprintTooltip": "Activa o desactiva la visibilitat de l'empremta de la imatge mostrada.", "additionalFootprintsTooltip": "Activa o desactiva la visibilitat de totes les empremtes addicionals que contenen la ubicació d'interès", "additionalCameraLocationsTooltip": "Activa o desactiva la visibilitat de totes les ubicacions de càmera addicionals d'imatges que contenen la ubicació d'interès.", "navigationTooltip": "Exploreu les imatges retornades en relació amb la ubicació d'interès.", "imageEnhancementTooltip": "<PERSON><PERSON><PERSON><PERSON> brillantor, el contrast i l'enfocament de la imatge.", "imageGalleryTooltip": "Exploreu les miniatures de totes les imatges on es mostra la ubicació d'interès.", "mapImageConversionTooltip": "<PERSON>u clic a la imatge per veure on es troba la ubicació al mapa i viceversa.", "unsupportedPanoramicImageryError": "Actualment, el visor no admet la visualització d'imatges {datasetFormat} en 360 graus.", "showPopups": "Mostra les finestres emergents"}