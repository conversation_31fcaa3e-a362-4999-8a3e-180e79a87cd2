{"label": "التحقق من صحة طبولوجيا الشبكة", "input": {"currentExtent": "النطاق الحالي", "entireExtent": "النطاق الكامل", "extentToValidate": "النطاق المطلوب التحقق من صحته", "validateTopology": "التحقق من الصحة"}, "status": {"executing": "يتم الآن التنفيذ", "loading": "تحميل", "success": "تم التحقق من الصحة بنجاح"}, "info": {"cannotAcquireVersionLock": "تم قفل إصدار الفرع بواسطة تطبيق آخر.", "noAdvancedEditingUserTypeExtension": "يفتقد الحساب الذي تم تسجيل الدخول إليه إلى ترخيص لملحق نوع المستخدم \"التحرير المتقدم\".", "noDirtyAreasInExtent": "لم يتم العثور على منطقة مهملة أثناء التحقق من الصحة.", "noDirtyAreasLayer": "تعذر العثور على طبقة المناطق المهملة. أضف طبقة المناطق المهملة إلى الخريطة.", "noUtilityNetwork": "لم يتم توفير شبكة مرافق. يجب تهيئة عنصر واجهة المستخدم باستخدام شبكة مرافق.", "noUtilityNetworkServiceUserTypeExtension": "يفتقد الحساب الذي تم تسجيل الدخول إليه إلى ترخيص لملحق نوع المستخدم ArcGIS Utility Network.", "noView": "لم يتم توفير MapView. تجب تهيئة عنصر واجهة المستخدم باستخدام MapView."}}