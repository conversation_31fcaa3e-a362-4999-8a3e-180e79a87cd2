{"title": "Penampil pencitraan yang diorientasi", "currentFootprint": "<PERSON><PERSON> saat ini", "additionalFootprints": "<PERSON><PERSON> ta<PERSON>han", "additionalCameraLocations": "<PERSON><PERSON> kamera tambahan", "imageAttributes": "Atribut gambar", "imageEnhancement": "Peningkatan gambar", "imageGallery": "<PERSON><PERSON> gambar", "float": "Mengambang", "dock": "Dok", "footprint": "<PERSON><PERSON>", "noImageError": "Tidak tersedia gambar untuk lokasi yang dipilih.", "onLoadMessage": "<PERSON><PERSON>h lokasi pada peta untuk melihat gambar.", "imageLoadError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat memuat gambar.", "mapImageConversionTool": "Alat lokasi gambar peta", "brightness": "<PERSON><PERSON><PERSON>", "contrast": "Kontras", "sharpness": "<PERSON><PERSON><PERSON><PERSON>", "navigationTool": "Alat navigasi", "noLocalPort": "Nilai untuk port lokal tidak disediakan. Port lokal diperlukan untuk memuat gambar lokal.", "noAttachment": "Tidak tersedia gambar atau lampiran untuk lokasi yang dipilih.", "currentFootprintTooltip": "<PERSON><PERSON><PERSON><PERSON>/matikan visibilitas tapak gambar yang ditampilkan.", "additionalFootprintsTooltip": "Hidup<PERSON>/matikan visibilitas semua tapak tambahan yang berisi lokasi yang diinginkan.", "additionalCameraLocationsTooltip": "Hidupkan/matikan visibilitas semua lokasi kamera gambar tambahan yang berisi lokasi yang diinginkan.", "navigationTooltip": "<PERSON><PERSON><PERSON><PERSON> gambar yang dikembalikan yang berhubungan dengan lokasi yang diinginkan.", "imageEnhancementTooltip": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, k<PERSON><PERSON>, dan ketajaman gambar.", "imageGalleryTooltip": "Je<PERSON>jahi gambar mini semua gambar yang menunjukkan lokasi yang di<PERSON>an.", "mapImageConversionTooltip": "Klik gambar untuk melihat tempat lokasi tersebut di peta, dan sebal<PERSON>.", "unsupportedPanoramicImageryError": "Viewer saat ini tidak mendukung visualisasi pencitraan {datasetFormat} 360-derajat.", "showPopups": "<PERSON><PERSON><PERSON><PERSON> pop-up"}