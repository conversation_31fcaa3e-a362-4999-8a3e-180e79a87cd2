{"title": "Visionneuse d’imagerie orientée", "currentFootprint": "Emprise actuelle", "additionalFootprints": "Emprises supplémentaires", "additionalCameraLocations": "Localisations supplémentaires de la caméra", "imageAttributes": "Attributs de l’image", "imageEnhancement": "Amélioration de l’image", "imageGallery": "Bibliothèque d’images", "float": "Flottant", "dock": "<PERSON><PERSON><PERSON>", "footprint": "Emprise", "noImageError": "Aucune image disponible pour la localisation sélectionnée.", "onLoadMessage": "Sélectionnez une localisation sur la carte pour afficher l’imagerie.", "imageLoadError": "Une erreur est survenue lors du chargement de l’image.", "mapImageConversionTool": "Outil Localisation carte-image", "brightness": "Luminosité", "contrast": "Contraste", "sharpness": "Netteté", "navigationTool": "Outil Navigation", "noLocalPort": "Aucune valeur n’est fournie pour le port local. Le port local est nécessaire pour charger des images locales.", "noAttachment": "Aucune image ou pièce jointe n’est disponible pour la localisation sélectionnée.", "currentFootprintTooltip": "Activer/désactiver la visibilité de l’emprise de l’image affichée.", "additionalFootprintsTooltip": "Activer/désactiver la visibilité de toutes les emprises supplémentaires contenant la localisation d’intérêt.", "additionalCameraLocationsTooltip": "Activer/désactiver la visibilité de toutes les localisations de caméra supplémentaires des images contenant la localisation d’intérêt.", "navigationTooltip": "Examiner les images renvoyées par rapport à la localisation d’intérêt.", "imageEnhancementTooltip": "Ajuster la luminosité, le contraste et la netteté de l’image.", "imageGalleryTooltip": "Examiner les miniatures de toutes les images illustrant la localisation d’intérêt.", "mapImageConversionTooltip": "Cliquer sur l’image pour voir où se trouve cette localisation sur la carte et inversement.", "unsupportedPanoramicImageryError": "Actuellement, la visionneuse ne prend pas en charge la visualisation de l’imagerie à 360 degrés au format {datasetFormat}.", "showPopups": "Afficher les fenêtres contextuelles"}