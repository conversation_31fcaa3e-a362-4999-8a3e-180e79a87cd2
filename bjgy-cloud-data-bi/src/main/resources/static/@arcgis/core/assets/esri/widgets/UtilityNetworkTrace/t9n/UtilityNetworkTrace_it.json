{"widgetLabel": "Tracciamento della rete di utilità", "widgetDescription": "Un widget per eseguire tracce in una rete di utilità.", "globalStrings": {"selectTerminalPlaceholder": "Selezionare terminali", "locateFeature": "<PERSON><PERSON><PERSON>", "zoomToFeature": "Zoom a", "clearResults": "Ignora", "nonSpatialObject": "Oggetto non spaziale"}, "inputsStrings": {"headerTabInputs": "Input", "headerStartingPoint": "Punti di partenza", "startingPointHint": "Aggiungere punti di partenza per la traccia.", "headerBarrier": "Barriere", "barrierButton": "Barriera", "barrierPointHint": "Aggiungere punti di barriera per la traccia.", "barrierFilter": "Barriera filtro", "addPointOption": "Aggiungi un punto", "addPointHint": "Clicca sulla mappa per aggiungere un punto", "removeAllPoints": "<PERSON><PERSON><PERSON><PERSON> tutto", "selectTraces": "Seleziona le tracce"}, "tracingStrings": {"traceOperation": "Tipi di tracce", "runTrace": "<PERSON><PERSON><PERSON><PERSON>"}, "resultsStrings": {"headerTabResults": "Risultati", "resultsListOptions": "Opzioni", "ellipsesOptions": "Opzioni", "displayAttribute": "Mostra l'attributo", "sortBy": "Ordina per", "startOverButton": "Deseleziona tutto", "startOverValidation": "Continuare? <PERSON>tti gli input e i risultati della traccia saranno persi.", "highlightTrace": "Mostra la grafica", "noSelectionResults": "Impossibile selezionare i record. Abilita invece a mostrare la grafica.", "viewFeatures": "Record", "selectFeatures": "Selezionare record", "graphicColor": "Colore", "functionHeader": "Funzioni", "noValue": "<PERSON><PERSON>un valore", "resultAreaHeader": "Mostrare l'area dei risultati", "resultAreaBuffer": "Distanza di buffer", "noFeaturesInMap": "Nessun record elencato perché mancano dalla mappa."}, "alertsStrings": {"startingPointAlertHeader": "Punto di partenza mancante", "startingPointAlert": "Aggiungi un punto di partenza per eseguire questa traccia.", "selectTraceAlertHeader": "Tipo di traccia non selezionato", "selectTraceAlert": "Seleziona un tipo di traccia da eseguire.", "NoRunAlertHeader": "<PERSON><PERSON>un risultato disponibile", "noResultsInfo": "Vai alla scheda Inputs per selezionare un tipo di traccia, aggiungi un punto di partenza e clicca su Run per visualizzare i risultati.", "traceExecuting": "Traccia in corso...", "genericResultHeader": "Risultato della traccia", "genericErrorHeader": "Si è verificato un errore", "noAssetsFoundHeader": "Impossibile aggiungere un punto", "noAssetFound": "Il punto deve intersecare una caratteristica sulla mappa.", "clearResultsAlert": "Continuare? Tutti i risultati di questa traccia saranno scartati.", "noTraceSupported": "Non è possibile trovare una Utility Network o il tracciamento non è supportato. Fornire una Utility Network valida con configurazioni di tracciamento condivise.", "noUserExtension": "L'account che ha effettuato l'accesso non dispone di licenza per l'estensione di tipo utente ArcGIS Utility Network.", "noTerminalDefinedHeader": "Terminale mancante", "noTerminalDefined": "È necessario selezionare almeno un terminale."}, "attributeStrings": {"traceId": "ID traccia", "traceName": "Nome traccia", "traceDescription": "Descrizione traccia", "startingPoints": "Punti di partenza", "barriers": "Barriere", "username": "Utente", "version": "Versione", "date": "Data", "elementCount": "Conteggio di record", "functionResult": "Funzioni di tracciamento", "areaStatistic": "Statistica dell'area", "globalid": "ID globale", "terminalid": "ID terminale", "x": "x", "y": "y"}}