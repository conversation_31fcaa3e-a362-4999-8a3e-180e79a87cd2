{"title": "Visor de imágenes orientadas", "currentFootprint": "<PERSON><PERSON>", "additionalFootprints": "Huellas adicionales", "additionalCameraLocations": "Ubicaciones de cámaras adicionales", "imageAttributes": "Atributos de imagen", "imageEnhancement": "<PERSON><PERSON><PERSON> de <PERSON>n", "imageGallery": "Galería de imágenes", "float": "Flotante", "dock": "Acoplar", "footprint": "<PERSON><PERSON>", "noImageError": "No hay ninguna imagen disponible para la ubicación seleccionada.", "onLoadMessage": "Seleccione una ubicación en el mapa para ver las imágenes.", "imageLoadError": "Se ha producido un error al cargar la imagen.", "mapImageConversionTool": "Herramienta Ubicación de mapa-imagen", "brightness": "<PERSON><PERSON><PERSON>", "contrast": "Contraste", "sharpness": "<PERSON><PERSON><PERSON>", "navigationTool": "Herramienta de navegación", "noLocalPort": "No se facilita un valor para el puerto local. El puerto local es necesario para cargar imágenes locales.", "noAttachment": "No hay ninguna imagen ni adjunto disponible para la ubicación seleccionada.", "currentFootprintTooltip": "Alterne la visibilidad de la huella de la imagen mostrada.", "additionalFootprintsTooltip": "Alterne la visibilidad de todas las huellas adicionales que contengan la ubicación de interés.", "additionalCameraLocationsTooltip": "Alterne la visibilidad de todas las ubicaciones de cámara adicionales de imágenes que contengan la ubicación de interés.", "navigationTooltip": "Explore las imágenes devueltas en relación con la ubicación de interés.", "imageEnhancementTooltip": "Ajuste el brillo, el contraste y la nitidez de la imagen.", "imageGalleryTooltip": "Explore vistas en miniatura de todas las imágenes que representan la ubicación de interés.", "mapImageConversionTooltip": "Haga clic en la imagen para ver dónde se encuentra esa ubicación en el mapa, y viceversa.", "unsupportedPanoramicImageryError": "Actualmente, Viewer no admite la visualización {datasetFormat} de imágenes a 360 grados.", "showPopups": "Mostrar ventanas emergentes"}