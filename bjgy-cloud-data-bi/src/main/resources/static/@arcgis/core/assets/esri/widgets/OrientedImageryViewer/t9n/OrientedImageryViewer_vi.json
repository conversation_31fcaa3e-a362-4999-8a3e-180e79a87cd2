{"title": "<PERSON><PERSON><PERSON><PERSON> xem hình ảnh định hướng", "currentFootprint": "Footprint hiện tại", "additionalFootprints": "Footprint b<PERSON> sung", "additionalCameraLocations": "<PERSON><PERSON><PERSON> vị trí m<PERSON><PERSON> b<PERSON> sung", "imageAttributes": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> h<PERSON>nh <PERSON>", "imageEnhancement": "<PERSON><PERSON><PERSON> cao chất l<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>", "imageGallery": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>", "float": "<PERSON><PERSON><PERSON>", "dock": "Ẩn", "footprint": "Footprint", "noImageError": "<PERSON><PERSON><PERSON><PERSON> có sẵn hình ảnh nào cho vị trí đã chọn.", "onLoadMessage": "<PERSON><PERSON><PERSON> một vị trí trên bản đồ để xem hình <PERSON>nh.", "imageLoadError": "<PERSON><PERSON> xảy ra lỗi khi tải hình ảnh.", "mapImageConversionTool": "<PERSON><PERSON><PERSON> cụ định vị bản đồ-<PERSON><PERSON><PERSON>nh", "brightness": "<PERSON><PERSON> sáng", "contrast": "<PERSON><PERSON> tư<PERSON> phản", "sharpness": "<PERSON><PERSON> n<PERSON>", "navigationTool": "<PERSON><PERSON><PERSON> cụ điều hướng", "noLocalPort": "<PERSON><PERSON><PERSON> trị cho cổng thông tin cục bộ không được cung cấp. <PERSON><PERSON><PERSON> có cổng thông tin cục bộ để tải hình <PERSON>nh cục bộ.", "noAttachment": "<PERSON><PERSON><PERSON><PERSON> có sẵn hình ảnh hoặc tệp đính kèm nào cho vị trí đã chọn.", "currentFootprintTooltip": "Chuyển đổi khả năng hiển thị footprint củ<PERSON> hình ảnh được hiển thị.", "additionalFootprintsTooltip": "Chuyển đổi khả năng hiển thị của tất cả các footprint bổ sung chứa vị trí quan tâm.", "additionalCameraLocationsTooltip": "Chuyển đổi khả năng hiển thị của tất cả các vị trí camera bổ sung của hình ảnh chứa vị trí quan tâm.", "navigationTooltip": "<PERSON><PERSON>á<PERSON> phá hình ảnh trả về liên quan đến vị trí quan tâm.", "imageEnhancementTooltip": "<PERSON><PERSON><PERSON><PERSON> chỉnh độ sáng, độ tương phản và độ sắc nét của hình ảnh.", "imageGalleryTooltip": "<PERSON>h<PERSON><PERSON> phá hình thu nhỏ của tất cả các hình ảnh mô tả vị trí quan tâm.", "mapImageConversionTooltip": "<PERSON><PERSON>ấ<PERSON> vào hình ảnh để xem vị trí đó ở đâu trên bản đồ và ngư<PERSON><PERSON> lại.", "unsupportedPanoramicImageryError": "Trình xem hiện không hỗ trợ hiển thị hình ảnh 360 độ {datasetFormat}.", "showPopups": "<PERSON><PERSON><PERSON> thị cửa sổ pop-up"}