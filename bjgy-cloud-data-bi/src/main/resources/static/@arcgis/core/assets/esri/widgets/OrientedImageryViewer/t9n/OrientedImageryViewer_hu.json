{"title": "Tájolt távérzékelt felvételek megtekintője", "currentFootprint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalFootprints": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalCameraLocations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imageAttributes": "Képattribútumok", "imageEnhancement": "Képjavítás", "imageGallery": "Képgaléria", "float": "Float", "dock": "Dokkolás", "footprint": "<PERSON><PERSON><PERSON>", "noImageError": "<PERSON><PERSON>ll rendelkezésre kép a kiválasztott helyhez.", "onLoadMessage": "A távérzékelt felvétel megtekintéséhez válassza ki a kívánt helyet a térképen.", "imageLoadError": "Hiba történt a kép betöltése közben.", "mapImageConversionTool": "Térkép-kép helymeghatározó eszköz", "brightness": "Fényerő", "contrast": "Kontraszt", "sharpness": "Élesség", "navigationTool": "Na<PERSON><PERSON><PERSON><PERSON><PERSON>", "noLocalPort": "A helyi port értéke nincs megadva. A helyi port a helyi képek betöltéséhez szükséges.", "noAttachment": "<PERSON><PERSON>ll rendelkezésre kép vagy csatolmány a kiválasztott helyhez.", "currentFootprintTooltip": "A megjelenített kép kiterjedésének láthatóságának átkapcsolása.", "additionalFootprintsTooltip": "A kívánt helyszínt tartalmazó további kiterjedés láthatóságának váltása.", "additionalCameraLocationsTooltip": "Az érdeklődésre számot tartó helyet tartalmazó képek további kamerahelyszíneinek láthatóságának átkapcsolása.", "navigationTooltip": "Vizsgálja meg a visszaküldött képeket a kívánt helyszínhez viszonyítva.", "imageEnhancementTooltip": "Állítsa be a kép fén<PERSON>t, kontrasztját és élességét.", "imageGalleryTooltip": "Tekintse meg a kívánt he<PERSON>ínt ábrázoló összes kép miniatűrjét.", "mapImageConversionTooltip": "<PERSON><PERSON><PERSON><PERSON> a k<PERSON><PERSON><PERSON>, anna<PERSON> me<PERSON>é<PERSON>z, hol van az adott hely a térképen, és fordítva.", "unsupportedPanoramicImageryError": "A Viewer jelenleg nem támogatja a(z) {datasetFormat} 360 fokos képek megjelenítését.", "showPopups": "Felugró ablakok megjelenítése"}