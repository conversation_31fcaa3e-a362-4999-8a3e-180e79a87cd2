{"title": "Yönlendirilmiş gör<PERSON><PERSON><PERSON> görü<PERSON>ici", "currentFootprint": "Geçerli taban alanı", "additionalFootprints": "Ek taban alanları", "additionalCameraLocations": "Ek kamera konumları", "imageAttributes": "Görüntü öznitelikleri", "imageEnhancement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imageGallery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "float": "yay", "dock": "Dok", "footprint": "<PERSON><PERSON> alanı", "noImageError": "Seçili konum için kullanılabilir görüntü yok.", "onLoadMessage": "Görüntüleri göstermek için haritada bir konum seçin.", "imageLoadError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bir hata oluş<PERSON>.", "mapImageConversionTool": "Harita-görüntü konum aracı", "brightness": "Parlaklık", "contrast": "<PERSON><PERSON><PERSON><PERSON>", "sharpness": "<PERSON><PERSON><PERSON>", "navigationTool": "Gezinti aracı", "noLocalPort": "<PERSON>rel bağlantı noktası için bir değer verilmedi. <PERSON><PERSON> görüntüleri yüklemek için bir yerel bağlantı noktası gereklidir.", "noAttachment": "Seçili konum için kullanılabilir görüntü veya ek yok.", "currentFootprintTooltip": "Görüntülenen görüntünün taban alanının görünürlüğünü açın/kapatın.", "additionalFootprintsTooltip": "İlgilenilen yeri içeren tüm ek taban alanlarının görünürlüğünü açın/kapatın.", "additionalCameraLocationsTooltip": "İlgilenilen yeri içeren görüntülerin tüm ek kamera konumlarının görünürlüğünü açın/kapatın.", "navigationTooltip": "İlgilenilen yerle ilişkili olarak döndürülen görüntüleri keşfedin.", "imageEnhancementTooltip": "G<PERSON>r<PERSON><PERSON><PERSON>ığını, kontrastı ve keskinliği ayarlayın.", "imageGalleryTooltip": "İlgilenilen yeri gösteren tüm görüntülerin küçük resimlerini keşfedin.", "mapImageConversionTooltip": "<PERSON>tada belirli bir konumun nerede olduğunu görmek için görüntünün üzerine tıklayın; tersi de geçerlidir.", "unsupportedPanoramicImageryError": "Viewer mevcut durumda {datasetFormat} 360 derecelik görüntülerin görselleştirilmesini desteklemiyor.", "showPopups": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}