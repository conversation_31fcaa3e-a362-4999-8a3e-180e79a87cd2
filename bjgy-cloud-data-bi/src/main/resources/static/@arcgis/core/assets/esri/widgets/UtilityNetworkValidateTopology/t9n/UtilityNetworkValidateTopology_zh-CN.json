{"label": "验证网络拓扑", "input": {"currentExtent": "当前范围", "entireExtent": "整个范围", "extentToValidate": "要验证的范围", "validateTopology": "验证"}, "status": {"executing": "正在执行", "loading": "正在加载", "success": "验证成功"}, "info": {"cannotAcquireVersionLock": "分支版本已被另一个应用程序锁定。", "noAdvancedEditingUserTypeExtension": "登录的帐户缺少高级编辑用户类型扩展模块的许可。", "noDirtyAreasInExtent": "验证期间未发现脏区。", "noDirtyAreasLayer": "未找到脏区图层。 向地图添加脏区图层。", "noUtilityNetwork": "未提供公共设施网络。 微件必须使用公共设施网络进行初始化。", "noUtilityNetworkServiceUserTypeExtension": "登录的帐户缺少 ArcGIS Utility Network 用户类型扩展模块的许可。", "noView": "未提供 MapView。 必须使用 MapView 对微件进行初始化。"}}