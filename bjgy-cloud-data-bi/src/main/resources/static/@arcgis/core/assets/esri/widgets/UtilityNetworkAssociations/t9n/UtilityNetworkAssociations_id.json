{"widgetLabel": "<PERSON><PERSON><PERSON><PERSON>", "input": {"enableAssociations": "<PERSON><PERSON>", "enableConnectivity": "Asosiasi konektivitas", "enableStructuralAttachment": "Asosiasi keterikatan struktural", "lineCap": "Cap", "lineColor": "<PERSON><PERSON>", "lineStyle": "Style", "lineWidth": "<PERSON><PERSON>", "maxAllowableAssociations": "<PERSON><PERSON><PERSON> maksimum as<PERSON>iasi yang diperbolehkan", "refreshAssociations": "<PERSON><PERSON><PERSON>", "showArrows": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"maxAllowableAssociationsExceeded": "Perbesar ke area yang lebih kecil atau ubah nilai opsi '<PERSON><PERSON><PERSON> maksimum asosiasi yang diperbolehkan'.", "noAssociationsFound": "Tidak ada asosiasi dalam jangkauan saat ini.", "noUtilityNetwork": "Utility network tidak disediakan. Widget harus diinisialisasi dengan utility network.", "noView": "MapView tidak tersedia. Widget harus diinisialisasi dengan MapView."}}