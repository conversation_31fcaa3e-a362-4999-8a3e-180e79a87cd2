{"title": "Oriented imagery viewer", "currentFootprint": "Current footprint", "additionalFootprints": "Additional footprints", "additionalCameraLocations": "Additional camera locations", "imageAttributes": "Image attributes", "imageEnhancement": "Image enhancement", "imageGallery": "Image gallery", "float": "Float", "dock": "Dock", "footprint": "Footprint", "noImageError": "No image available for the selected location.", "onLoadMessage": "Select a location on map to view imagery.", "imageLoadError": "Error occurred while loading the image.", "mapImageConversionTool": "Map-image location tool", "brightness": "Brightness", "contrast": "Contrast", "sharpness": "Sharpness", "navigationTool": "Navigation tool", "noLocalPort": "A value for local port is not provided. Local port is required to load local images.", "noAttachment": "No image or attachment available for the selected location.", "currentFootprintTooltip": "Toggle visibility of the displayed image's footprint.", "additionalFootprintsTooltip": "Toggle visibility of all additional footprints containing the location of interest.", "additionalCameraLocationsTooltip": "Toggle visibility of all additional camera locations of images containing the location of interest.", "navigationTooltip": "Explore returned images in relation to the location of interest.", "imageEnhancementTooltip": "Adjust image brightness, contrast, and sharpening.", "imageGalleryTooltip": "Explore thumbnails of all images depicting the location of interest.", "mapImageConversionTooltip": "Click on the image to see where that location is on the map, and vice versa.", "unsupportedPanoramicImageryError": "Viewer currently does not support visualizing {datasetFormat} 360-degree imagery.", "showPopups": "Show pop-ups"}