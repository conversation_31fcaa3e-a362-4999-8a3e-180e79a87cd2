#!/bin/bash

# 修复所有Spring Boot模块的maven插件配置，添加mainClass配置

# 定义模块和对应的主类
declare -A modules=(
    ["bjgy-cloud-gateway"]="com.bjgy.GatewayApplication"
    ["bjgy-cloud-system"]="com.bjgy.SystemApplication"
    ["bjgy-cloud-data-integrate"]="com.bjgy.DataIntegrateApplication"
    ["bjgy-cloud-data-development/bjgy-cloud-develop-server"]="com.bjgy.DataDevelopmentApplication"
    ["bjgy-cloud-data-service"]="com.bjgy.DataServiceApplication"
    ["bjgy-cloud-data-governance"]="com.bjgy.DataGovernanceApplication"
    ["bjgy-cloud-data-assets"]="com.bjgy.DataAssetsApplication"
    ["bjgy-cloud-data-bi"]="com.bjgy.DataBiApplication"
    ["bjgy-cloud-module/bjgy-cloud-monitor"]="com.bjgy.MonitorApplication"
)

echo "开始修复Spring Boot Maven插件配置..."

for module in "${!modules[@]}"; do
    pom_file="./${module}/pom.xml"
    main_class="${modules[$module]}"
    
    if [ -f "$pom_file" ]; then
        echo "处理模块: $module (主类: $main_class)"
        
        # 检查是否已经有mainClass配置
        if grep -q "<mainClass>" "$pom_file"; then
            echo "  - 已存在mainClass配置，跳过"
            continue
        fi
        
        # 检查是否有spring-boot-maven-plugin
        if grep -q "spring-boot-maven-plugin" "$pom_file"; then
            # 在<configuration>标签后添加mainClass
            sed -i "s|<configuration>|<configuration>\n                            <mainClass>$main_class</mainClass>|" "$pom_file"
            echo "  - 已添加mainClass配置"
        else
            echo "  - 未找到spring-boot-maven-plugin，跳过"
        fi
    else
        echo "警告: 未找到文件 $pom_file"
    fi
done

echo "Spring Boot Maven插件配置修复完成！"
