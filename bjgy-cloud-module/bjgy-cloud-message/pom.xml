<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.bjgy</groupId>
		<artifactId>bjgy-cloud-module</artifactId>
		<version>2.0.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>bjgy-cloud-message</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.bjgy</groupId>
			<artifactId>bjgy-cloud-mybatis</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.bjgy</groupId>
			<artifactId>bjgy-cloud-api</artifactId>
			<version>2.0.0</version>
		</dependency>
        <!--使用log42j-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-springdoc-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>dysmsapi20170525</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.qiniu</groupId>
			<artifactId>qiniu-java-sdk</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>2.6.11</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>tar.gz</id>
            <build>
                <!--<finalName>${project.artifactId}</finalName>-->
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>appassembler-maven-plugin</artifactId>
                        <version>2.1.0</version>
                        <!-- 如果不配置 generate-daemons，则打包命令为 mvn clean package appassembler:assemble -->
                        <!-- 如果配置了 generate-daemons，打包命令可以是 mvn clean package 也可以是 mvn clean package appassembler:assemble -->
                        <executions>
                            <execution>
                                <id>generate-jsw-scripts</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>generate-daemons</goal>
                                </goals>
                            </execution>
                        </executions>

                        <configuration>
                            <!-- flat与lib共同决定将项目用的的所有jar包复制到lib目录下 -->
                            <repositoryLayout>flat</repositoryLayout>
                            <!--从哪里copy配置文件-->
                            <configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
                            <includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
                            <!--是否copy配置文件-->
                            <copyConfigurationDirectory>true</copyConfigurationDirectory>
                            <!--配置文件存放在conf目录路径-->
                            <configurationDirectory>conf</configurationDirectory>
                            <!-- 打包的jar，以及maven依赖的jar放到这个目录里面 -->
                            <repositoryName>lib</repositoryName>
                            <!-- 可执行脚本的目录 -->
                            <binFolder>bin</binFolder>
                            <encoding>UTF-8</encoding>
                            <logsDirectory>logs</logsDirectory>

                            <daemons>
                                <daemon>
                                    <id>${project.artifactId}</id>
                                    <mainClass>com.bjgy.MessageApplication</mainClass>
                                    <platforms>
                                        <platform>jsw</platform>
                                    </platforms>
                                    <generatorConfigurations>
                                        <generatorConfiguration>
                                            <generator>jsw</generator>
                                            <includes>
                                                <include>linux-x86-32</include>
                                                <include>linux-x86-64</include>
                                                <include>windows-x86-32</include>
                                                <include>windows-x86-64</include>
                                            </includes>
                                            <configuration>
                                                <property>
                                                    <name>configuration.directory.in.classpath.first</name>
                                                    <value>conf</value>
                                                </property>
                                                <property>
                                                    <name>wrapper.ping.timeout</name>
                                                    <value>120</value>
                                                </property>
                                                <property>
                                                    <name>set.default.REPO_DIR</name>
                                                    <value>lib</value>
                                                </property>
                                                <property>
                                                    <name>wrapper.logfile</name>
                                                    <value>logs/wrapper.log</value>
                                                </property>
                                            </configuration>
                                        </generatorConfiguration>
                                    </generatorConfigurations>
                                    <jvmSettings>
                                        <!-- jvm参数 -->
                                        <!--<systemProperties>
                                            <systemProperty>com.sun.management.jmxremote</systemProperty>
                                            <systemProperty>com.sun.management.jmxremote.port=1984</systemProperty>
                                            <systemProperty>com.sun.management.jmxremote.authenticate=false</systemProperty>
                                            <systemProperty>com.sun.management.jmxremote.ssl=false</systemProperty>
                                        </systemProperties>-->
                                        <extraArguments>
                                            <extraArgument>-server</extraArgument>
                                            <extraArgument>-Dfile.encoding=utf-8</extraArgument>
                                            <extraArgument>-Xms128m</extraArgument>
                                            <extraArgument>-Xmx256m</extraArgument>
                                            <extraArgument>-XX:+PrintGCDetails</extraArgument><!--输出GC的详细日志-->
                                            <extraArgument>-XX:+PrintGCDateStamps</extraArgument><!--输出GC的时间戳-->
                                            <extraArgument>-Xloggc:logs/gc.log</extraArgument><!--日志文件的输出路径-->
                                        </extraArguments>
                                    </jvmSettings>
                                </daemon>
                            </daemons>
                            <programs>
                                <program>
                                    <mainClass>com.bjgy.MessageApplication</mainClass>
                                    <id>${project.artifactId}</id>
                                </program>
                            </programs>
                        </configuration>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>win</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <outputDirectory>../../target/win</outputDirectory>
                            <descriptors>
                                <descriptor>${project.parent.parent.basedir}/assembly/assembly-win.xml</descriptor>
                            </descriptors>
                        </configuration>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>linux</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <outputDirectory>../../target/linux</outputDirectory>
                            <descriptors>
                                <descriptor>${project.parent.parent.basedir}/assembly/assembly-linux.xml</descriptor>
                            </descriptors>
                        </configuration>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>all-os</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <outputDirectory>../../target</outputDirectory>
                            <descriptors>
                                <descriptor>${project.parent.parent.basedir}/assembly/assembly-win.xml</descriptor>
                                <descriptor>${project.parent.parent.basedir}/assembly/assembly-linux.xml</descriptor>
                            </descriptors>
                        </configuration>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jar</id>
            <build>
                <finalName>${artifactId}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-resources-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <source>${maven.compiler.source}</source>
                            <target>${maven.compiler.target}</target>
                            <encoding>utf-8</encoding>
                            <showWarnings>false</showWarnings>
                            <fork>false</fork>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!--true跳过测试-->
                            <skipTests>true</skipTests>
                            <encoding>utf-8</encoding>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <arguments>
                                <argument>-server</argument>
                                <argument>-Dfile.encoding=utf-8</argument>
                                <argument>-Xms128m</argument>
                                <argument>-Xmx256m</argument>
                                <argument>-XX:+PrintGCDetails</argument><!--输出GC的详细日志-->
                                <argument>-XX:+PrintGCDateStamps</argument><!--输出GC的时间戳-->
                                <argument>-Xloggc:logs/gc.log</argument>
                            </arguments>
                            <outputDirectory>../../target/jar</outputDirectory>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <includes>
                            <include>**/*</include>
                        </includes>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>

</project>
