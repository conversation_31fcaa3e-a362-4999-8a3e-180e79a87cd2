#!/bin/bash

# 修复所有包含 all-os profile 的 pom.xml 文件
files=(
    "./bjgy-cloud-data-assets/pom.xml"
    "./bjgy-cloud-data-bi/pom.xml"
    "./bjgy-cloud-data-development/bjgy-cloud-develop-flink-1.14/pom.xml"
    "./bjgy-cloud-data-development/bjgy-cloud-develop-flink-1.16/pom.xml"
    "./bjgy-cloud-data-development/bjgy-cloud-develop-server/pom.xml"
    "./bjgy-cloud-data-governance/pom.xml"
    "./bjgy-cloud-data-integrate/pom.xml"
    "./bjgy-cloud-data-service/pom.xml"
    "./bjgy-cloud-gateway/pom.xml"
    "./bjgy-cloud-module/bjgy-cloud-monitor/pom.xml"
    "./bjgy-cloud-system/pom.xml"
)

for file in "${files[@]}"; do
    echo "Fixing $file..."
    sed -i 's/<activeByDefault>true<\/activeByDefault>/<activeByDefault>false<\/activeByDefault>/g' "$file"
done

echo "All files fixed!"
